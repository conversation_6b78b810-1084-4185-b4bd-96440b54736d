#!/usr/bin/env python3
"""
Live2D语音对话系统 - 语音处理模块

这个模块提供了完整的语音处理功能，包括：
- PyAudio麦克风管理
- 按键触发语音输入
- 实时语音输入
- 语音数据处理和队列管理

使用示例：
    from dialogue_system.voice import MicrophoneManager, VoiceProcessor
    
    # 创建麦克风管理器
    mic_manager = MicrophoneManager()
    
    # 创建语音处理器
    voice_processor = VoiceProcessor(mic_manager)
    
    # 开始语音输入
    voice_processor.start_key_triggered_mode()
    voice_processor.start_realtime_mode()

高级使用示例：
    from dialogue_system.voice import VoiceDialogueManager
    
    # 创建语音对话管理器（一站式解决方案）
    voice_manager = VoiceDialogueManager(config_manager)
    
    # 切换到按键触发模式
    voice_manager.switch_to_key_triggered_mode()
    
    # 切换到实时对话模式
    voice_manager.switch_to_realtime_mode()
"""

# 导入核心组件
from .microphone_manager import MicrophoneManager
from .voice_processor import VoiceProcessor
from .key_triggered_input import KeyTriggeredInput
from .realtime_input import RealtimeInput

__version__ = "1.0.0"
__author__ = "Live2D Voice Dialogue Team"


class VoiceDialogueManager:
    """语音对话管理器 - 提供一站式语音对话功能管理"""

    def __init__(self, config_manager=None):
        """初始化语音对话管理器"""
        self.config_manager = config_manager

        # 初始化核心组件
        self.microphone_manager = MicrophoneManager(config_manager)
        self.voice_processor = VoiceProcessor(self.microphone_manager, config_manager)

        # 初始化输入模式
        self.key_triggered_input = KeyTriggeredInput(self.voice_processor, config_manager)
        self.realtime_input = RealtimeInput(self.voice_processor, config_manager)

        # 当前模式
        self.current_mode = None
        self.is_active = False

        # 回调函数
        self.on_voice_input = None
        self.on_voice_error = None
        self.on_tts_start = None
        self.on_tts_finish = None

    def set_callbacks(self,
                     on_voice_input=None,
                     on_voice_error=None,
                     on_tts_start=None,
                     on_tts_finish=None):
        """设置回调函数"""
        self.on_voice_input = on_voice_input
        self.on_voice_error = on_voice_error
        self.on_tts_start = on_tts_start
        self.on_tts_finish = on_tts_finish

        # 设置子组件的回调
        self.key_triggered_input.set_callbacks(
            on_audio_ready=self._on_audio_ready,
            on_status_change=self._on_status_change
        )
        self.realtime_input.set_callbacks(
            on_audio_ready=self._on_audio_ready,
            on_status_change=self._on_status_change
        )

    def set_mode(self, mode: str):
        """设置语音输入模式"""
        if mode == "key_triggered":
            self.switch_to_key_triggered_mode()
        elif mode == "realtime":
            self.switch_to_realtime_mode()
        else:
            raise ValueError(f"不支持的模式: {mode}")

    def stop(self):
        """停止语音对话"""
        self.stop_voice_dialogue()

    def reload_config(self):
        """重新加载配置"""
        # 重新加载各组件的配置
        if hasattr(self.microphone_manager, 'reload_config'):
            self.microphone_manager.reload_config()
        if hasattr(self.voice_processor, 'reload_config'):
            self.voice_processor.reload_config()
        if hasattr(self.key_triggered_input, 'reload_config'):
            self.key_triggered_input.reload_config()
        if hasattr(self.realtime_input, 'reload_config'):
            self.realtime_input.reload_config()
        print("🔄 语音对话配置已重新加载")

    def _on_audio_ready(self, audio_data):
        """音频数据准备就绪回调"""
        if self.on_voice_input:
            self.on_voice_input(audio_data)

    def _on_status_change(self, status):
        """状态变化回调"""
        print(f"📢 语音状态: {status}")

    def speak_text(self, text: str):
        """使用TTS播放文本"""
        print(f"🎵 TTS播放: {text}")
        # TODO: 实现TTS功能
        # 这里应该调用TTS管理器来播放文本
        # 目前只是占位实现
        if self.on_tts_start:
            self.on_tts_start()

        # 模拟TTS播放完成
        if self.on_tts_finish:
            self.on_tts_finish()

    def switch_to_key_triggered_mode(self):
        """切换到按键触发模式"""
        if self.current_mode == "key_triggered":
            return
            
        # 停止当前模式
        self.stop_current_mode()
        
        # 启动按键触发模式
        self.current_mode = "key_triggered"
        self.key_triggered_input.start()
        print("🎤 已切换到按键触发模式")
        
    def switch_to_realtime_mode(self):
        """切换到实时对话模式"""
        if self.current_mode == "realtime":
            return
            
        # 停止当前模式
        self.stop_current_mode()
        
        # 启动实时对话模式
        self.current_mode = "realtime"
        self.realtime_input.start()
        print("🎤 已切换到实时对话模式")
        
    def stop_current_mode(self):
        """停止当前模式"""
        if self.current_mode == "key_triggered":
            self.key_triggered_input.stop()
        elif self.current_mode == "realtime":
            self.realtime_input.stop()
            
        self.current_mode = None
        
    def start_voice_dialogue(self):
        """开始语音对话"""
        if not self.is_active:
            self.microphone_manager.start()
            self.is_active = True
            print("🎤 语音对话系统已启动")
            
    def stop_voice_dialogue(self):
        """停止语音对话"""
        if self.is_active:
            self.stop_current_mode()
            self.microphone_manager.stop()
            self.is_active = False
            print("🎤 语音对话系统已停止")
            
    def get_current_mode(self):
        """获取当前模式"""
        return self.current_mode
        
    def is_voice_dialogue_active(self):
        """检查语音对话是否激活"""
        return self.is_active


# 导出主要类
__all__ = [
    'MicrophoneManager',
    'VoiceProcessor', 
    'KeyTriggeredInput',
    'RealtimeInput',
    'VoiceDialogueManager'
]
