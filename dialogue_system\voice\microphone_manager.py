#!/usr/bin/env python3
"""
Live2D语音对话系统 - PyAudio麦克风管理器

这个模块提供了完整的麦克风管理功能，包括：
- PyAudio音频流管理
- 音频设备检测和选择
- 实时音频数据采集
- 音频参数配置

使用示例：
    from dialogue_system.voice.microphone_manager import MicrophoneManager
    
    # 创建麦克风管理器
    mic_manager = MicrophoneManager()
    
    # 开始音频流
    mic_manager.start()
    
    # 读取音频数据
    audio_data = mic_manager.read_audio()
    
    # 停止音频流
    mic_manager.stop()
"""

import pyaudio
import numpy as np
import threading
import queue
import time
from typing import Optional, List, Dict, Any


class MicrophoneManager:
    """PyAudio麦克风管理器"""
    
    def __init__(self, config_manager=None):
        """初始化麦克风管理器"""
        self.config_manager = config_manager
        
        # 默认音频配置
        self.default_config = {
            "sample_rate": 16000,
            "channels": 1,
            "chunk_size": 1024,
            "format": pyaudio.paInt16,
            "input_device_index": None
        }
        
        # 加载配置
        self.audio_config = self._load_audio_config()
        
        # PyAudio对象
        self.pyaudio_instance = None
        self.audio_stream = None
        
        # 音频数据队列
        self.audio_queue = queue.Queue(maxsize=100)
        
        # 状态控制
        self.is_running = False
        self.is_recording = False
        self.stream_thread = None
        
        # 音频设备信息
        self.available_devices = []
        
        print("🎤 麦克风管理器初始化完成")
        
    def _load_audio_config(self) -> Dict[str, Any]:
        """加载音频配置"""
        if self.config_manager:
            # 从配置管理器加载
            voice_config = self.config_manager.config.get("voice_dialogue", {})
            microphone_config = voice_config.get("microphone_config", {})

            # 合并默认配置和用户配置
            config = self.default_config.copy()
            config.update(microphone_config)

            # 修复格式字段的类型转换
            if "format" in config and isinstance(config["format"], str):
                format_map = {
                    "paInt16": pyaudio.paInt16,
                    "paInt24": pyaudio.paInt24,
                    "paInt32": pyaudio.paInt32,
                    "paFloat32": pyaudio.paFloat32,
                    "int16": pyaudio.paInt16,
                    "int24": pyaudio.paInt24,
                    "int32": pyaudio.paInt32,
                    "float32": pyaudio.paFloat32
                }
                config["format"] = format_map.get(config["format"], pyaudio.paInt16)

            # 确保数值类型正确
            if "sample_rate" in config:
                config["sample_rate"] = int(config["sample_rate"])
            if "channels" in config:
                config["channels"] = int(config["channels"])
            if "chunk_size" in config:
                config["chunk_size"] = int(config["chunk_size"])

            return config
        else:
            return self.default_config.copy()
            
    def initialize_pyaudio(self) -> bool:
        """初始化PyAudio"""
        try:
            if self.pyaudio_instance is None:
                self.pyaudio_instance = pyaudio.PyAudio()
                print("✅ PyAudio初始化成功")
                
                # 扫描可用设备
                self._scan_audio_devices()
                return True
        except Exception as e:
            print(f"❌ PyAudio初始化失败: {e}")
            return False
            
    def _scan_audio_devices(self):
        """扫描可用的音频输入设备"""
        if not self.pyaudio_instance:
            return
            
        self.available_devices = []
        device_count = self.pyaudio_instance.get_device_count()
        
        print("🔍 扫描音频输入设备:")
        for i in range(device_count):
            try:
                device_info = self.pyaudio_instance.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    self.available_devices.append({
                        'index': i,
                        'name': device_info['name'],
                        'channels': device_info['maxInputChannels'],
                        'sample_rate': device_info['defaultSampleRate']
                    })
                    print(f"  📱 设备 {i}: {device_info['name']} "
                          f"(通道: {device_info['maxInputChannels']}, "
                          f"采样率: {device_info['defaultSampleRate']})")
            except Exception as e:
                print(f"  ⚠️ 设备 {i} 信息获取失败: {e}")
                
    def get_available_devices(self) -> List[Dict[str, Any]]:
        """获取可用的音频输入设备列表"""
        return self.available_devices.copy()
        
    def set_input_device(self, device_index: Optional[int]):
        """设置输入设备"""
        if device_index is not None:
            # 验证设备索引
            if any(device['index'] == device_index for device in self.available_devices):
                self.audio_config['input_device_index'] = device_index
                print(f"🎤 已设置输入设备: {device_index}")
            else:
                print(f"⚠️ 无效的设备索引: {device_index}")
        else:
            self.audio_config['input_device_index'] = None
            print("🎤 使用默认输入设备")
            
    def start(self) -> bool:
        """开始音频流"""
        if self.is_running:
            print("⚠️ 音频流已在运行")
            return True
            
        # 初始化PyAudio
        if not self.initialize_pyaudio():
            return False
            
        try:
            # 创建音频流
            self.audio_stream = self.pyaudio_instance.open(
                format=self.audio_config['format'],
                channels=self.audio_config['channels'],
                rate=self.audio_config['sample_rate'],
                input=True,
                input_device_index=self.audio_config['input_device_index'],
                frames_per_buffer=self.audio_config['chunk_size'],
                stream_callback=self._audio_callback
            )
            
            # 启动音频流
            self.audio_stream.start_stream()
            self.is_running = True
            
            print(f"✅ 音频流已启动 (采样率: {self.audio_config['sample_rate']}Hz, "
                  f"块大小: {self.audio_config['chunk_size']})")
            return True
            
        except Exception as e:
            print(f"❌ 音频流启动失败: {e}")
            return False
            
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """音频流回调函数"""
        if status:
            print(f"⚠️ 音频流状态警告: {status}")
            
        # 将音频数据放入队列
        try:
            audio_array = np.frombuffer(in_data, dtype=np.int16)
            self.audio_queue.put_nowait(audio_array)
        except queue.Full:
            # 队列满时丢弃最旧的数据
            try:
                self.audio_queue.get_nowait()
                self.audio_queue.put_nowait(audio_array)
            except queue.Empty:
                pass
                
        return (None, pyaudio.paContinue)
        
    def read_audio(self, timeout: float = 0.1) -> Optional[np.ndarray]:
        """读取音频数据"""
        try:
            return self.audio_queue.get(timeout=timeout)
        except queue.Empty:
            return None
            
    def clear_audio_buffer(self):
        """清空音频缓冲区"""
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except queue.Empty:
                break
        print("🧹 音频缓冲区已清空")
        
    def stop(self):
        """停止音频流"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 停止音频流
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
                self.audio_stream = None
                print("✅ 音频流已停止")
            except Exception as e:
                print(f"⚠️ 停止音频流时出错: {e}")
                
        # 清空队列
        self.clear_audio_buffer()
        
    def cleanup(self):
        """清理资源"""
        self.stop()
        
        if self.pyaudio_instance:
            try:
                self.pyaudio_instance.terminate()
                self.pyaudio_instance = None
                print("✅ PyAudio资源已清理")
            except Exception as e:
                print(f"⚠️ 清理PyAudio资源时出错: {e}")
                
    def get_audio_level(self) -> float:
        """获取当前音频音量级别 (0.0 - 1.0)"""
        audio_data = self.read_audio(timeout=0.01)
        if audio_data is not None:
            # 计算RMS音量
            rms = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
            # 归一化到0-1范围
            normalized_level = min(rms / 32767.0, 1.0)
            return normalized_level
        return 0.0
        
    def is_audio_active(self, threshold: float = 0.01) -> bool:
        """检测是否有音频活动"""
        level = self.get_audio_level()
        return level > threshold
        
    def __del__(self):
        """析构函数"""
        self.cleanup()
