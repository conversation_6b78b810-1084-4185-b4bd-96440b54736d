#!/usr/bin/env python3
"""
Live2D语音对话系统 - 语音处理器

这个模块提供了语音数据处理功能，包括：
- 音频数据收集和缓存
- 语音活动检测
- 音频格式转换
- 语音数据预处理

使用示例：
    from dialogue_system.voice.voice_processor import VoiceProcessor
    from dialogue_system.voice.microphone_manager import MicrophoneManager
    
    # 创建语音处理器
    mic_manager = MicrophoneManager()
    voice_processor = VoiceProcessor(mic_manager)
    
    # 开始录音
    voice_processor.start_recording()
    
    # 停止录音并获取数据
    audio_data = voice_processor.stop_recording()
"""

import numpy as np
import threading
import time
import io
import wave
from typing import Optional, List, Callable, Any
from .microphone_manager import MicrophoneManager


class VoiceProcessor:
    """语音处理器 - 处理音频数据收集和预处理"""
    
    def __init__(self, microphone_manager: MicrophoneManager, config_manager=None):
        """初始化语音处理器"""
        self.microphone_manager = microphone_manager
        self.config_manager = config_manager
        
        # 默认处理配置
        self.default_config = {
            "volume_threshold": 0.01,
            "silence_duration": 2.0,
            "min_speech_duration": 0.5,
            "max_speech_duration": 30.0,
            "sample_rate": 16000
        }
        
        # 加载配置
        self.processing_config = self._load_processing_config()
        
        # 录音状态
        self.is_recording = False
        self.recording_thread = None
        self.recorded_audio = []
        
        # 语音活动检测
        self.last_audio_time = 0
        self.speech_start_time = 0
        self.silence_start_time = 0
        
        # 回调函数
        self.on_speech_start: Optional[Callable] = None
        self.on_speech_end: Optional[Callable] = None
        self.on_audio_data: Optional[Callable] = None
        
        # 线程锁
        self.recording_lock = threading.Lock()
        
        print("🎙️ 语音处理器初始化完成")
        
    def _load_processing_config(self) -> dict:
        """加载处理配置"""
        if self.config_manager:
            voice_config = self.config_manager.config.get("voice_dialogue", {})
            processing_config = voice_config.get("processing_config", {})
            
            config = self.default_config.copy()
            config.update(processing_config)
            return config
        else:
            return self.default_config.copy()
            
    def set_callbacks(self, 
                     on_speech_start: Optional[Callable] = None,
                     on_speech_end: Optional[Callable] = None,
                     on_audio_data: Optional[Callable] = None):
        """设置回调函数"""
        self.on_speech_start = on_speech_start
        self.on_speech_end = on_speech_end
        self.on_audio_data = on_audio_data
        
    def start_recording(self) -> bool:
        """开始录音"""
        with self.recording_lock:
            if self.is_recording:
                print("⚠️ 录音已在进行中")
                return False
                
            # 确保麦克风管理器已启动
            if not self.microphone_manager.is_running:
                if not self.microphone_manager.start():
                    print("❌ 无法启动麦克风")
                    return False
                    
            # 清空之前的录音数据
            self.recorded_audio = []
            self.microphone_manager.clear_audio_buffer()
            
            # 开始录音
            self.is_recording = True
            self.speech_start_time = time.time()
            print(f"🕐 录音开始时间: {self.speech_start_time}")
            
            # 启动录音线程
            self.recording_thread = threading.Thread(target=self._recording_loop)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            
            print("🎙️ 开始录音")
            
            # 触发回调
            if self.on_speech_start:
                self.on_speech_start()
                
            return True
            
    def stop_recording(self) -> Optional[np.ndarray]:
        """停止录音并返回音频数据"""
        with self.recording_lock:
            if not self.is_recording:
                print("⚠️ 当前没有在录音")
                return None
                
            self.is_recording = False
            
            # 等待录音线程结束
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=1.0)
                
            print("🎙️ 录音已停止")
            
            # 触发回调
            if self.on_speech_end:
                self.on_speech_end()
                
            # 返回录音数据
            if self.recorded_audio:
                audio_data = np.concatenate(self.recorded_audio)
                print(f"📊 录音数据长度: {len(audio_data)} 样本 "
                      f"({len(audio_data) / self.processing_config['sample_rate']:.2f}秒)")
                return audio_data
            else:
                print("⚠️ 没有录音数据")
                return None
                
    def _recording_loop(self):
        """录音循环"""
        while self.is_recording:
            # 读取音频数据
            audio_data = self.microphone_manager.read_audio(timeout=0.1)
            
            if audio_data is not None:
                # 添加到录音缓存
                self.recorded_audio.append(audio_data.copy())
                
                # 触发音频数据回调
                if self.on_audio_data:
                    self.on_audio_data(audio_data)
                    
                # 检查录音时长限制
                current_time = time.time()
                recording_duration = current_time - self.speech_start_time
                
                if recording_duration > self.processing_config['max_speech_duration']:
                    print(f"⏰ 录音时长超过限制 ({self.processing_config['max_speech_duration']}秒)，自动停止")
                    break
                    
            time.sleep(0.01)  # 避免过度占用CPU
            
    def detect_voice_activity(self) -> bool:
        """检测语音活动"""
        audio_level = self.microphone_manager.get_audio_level()
        current_time = time.time()
        
        # 检查是否超过音量阈值
        if audio_level > self.processing_config['volume_threshold']:
            self.last_audio_time = current_time
            return True
        else:
            # 检查静音持续时间
            silence_duration = current_time - self.last_audio_time
            return silence_duration < self.processing_config['silence_duration']
            
    def wait_for_speech_start(self, timeout: float = 30.0) -> bool:
        """等待语音开始"""
        start_time = time.time()
        
        print("👂 等待语音输入...")
        
        while time.time() - start_time < timeout:
            if self.microphone_manager.is_audio_active(
                self.processing_config['volume_threshold']
            ):
                print("🎤 检测到语音输入")
                return True
            time.sleep(0.1)
            
        print("⏰ 等待语音输入超时")
        return False
        
    def wait_for_speech_end(self, timeout: float = 30.0) -> bool:
        """等待语音结束"""
        last_activity_time = time.time()
        
        print("👂 等待语音结束...")
        
        while time.time() - last_activity_time < timeout:
            if self.microphone_manager.is_audio_active(
                self.processing_config['volume_threshold']
            ):
                # 有语音活动，更新时间
                last_activity_time = time.time()
            else:
                # 检查静音持续时间
                silence_duration = time.time() - last_activity_time
                if silence_duration >= self.processing_config['silence_duration']:
                    print("🔇 检测到语音结束")
                    return True
                    
            time.sleep(0.1)
            
        print("⏰ 等待语音结束超时")
        return False
        
    def convert_to_wav_bytes(self, audio_data: np.ndarray) -> bytes:
        """将音频数据转换为WAV格式字节"""
        if audio_data is None or len(audio_data) == 0:
            return b''
            
        # 创建内存中的WAV文件
        wav_buffer = io.BytesIO()
        
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(self.processing_config['sample_rate'])
            wav_file.writeframes(audio_data.astype(np.int16).tobytes())
            
        wav_buffer.seek(0)
        return wav_buffer.read()
        
    def save_audio_to_file(self, audio_data: np.ndarray, filename: str) -> bool:
        """保存音频数据到文件"""
        try:
            with wave.open(filename, 'wb') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(self.processing_config['sample_rate'])
                wav_file.writeframes(audio_data.astype(np.int16).tobytes())
                
            print(f"💾 音频已保存到: {filename}")
            return True
        except Exception as e:
            print(f"❌ 保存音频文件失败: {e}")
            return False
            
    def get_audio_info(self, audio_data: np.ndarray) -> dict:
        """获取音频信息"""
        if audio_data is None or len(audio_data) == 0:
            return {}
            
        duration = len(audio_data) / self.processing_config['sample_rate']
        rms = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
        max_amplitude = np.max(np.abs(audio_data))
        
        return {
            'duration': duration,
            'samples': len(audio_data),
            'sample_rate': self.processing_config['sample_rate'],
            'rms_level': rms,
            'max_amplitude': max_amplitude,
            'normalized_rms': rms / 32767.0,
            'normalized_max': max_amplitude / 32767.0
        }
        
    def is_valid_speech(self, audio_data: np.ndarray) -> bool:
        """检查是否为有效的语音数据"""
        if audio_data is None or len(audio_data) == 0:
            return False
            
        # 检查最小时长
        duration = len(audio_data) / self.processing_config['sample_rate']
        if duration < self.processing_config['min_speech_duration']:
            print(f"⚠️ 语音时长过短: {duration:.2f}秒")
            return False
            
        # 检查音量级别
        rms = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
        normalized_rms = rms / 32767.0
        
        if normalized_rms < self.processing_config['volume_threshold']:
            print(f"⚠️ 语音音量过低: {normalized_rms:.4f}")
            return False
            
        return True
