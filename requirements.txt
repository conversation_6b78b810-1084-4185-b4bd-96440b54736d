# Live2D语音对话系统依赖包

# 核心框架
PySide6>=6.5.0
PyQt6>=6.5.0

# 音频处理
pyaudio>=0.2.11
numpy>=1.21.0

# 语音识别 (STT)
faster-whisper>=0.10.0
torch>=2.0.0
torchaudio>=2.0.0

# 语音合成 (TTS) - API调用
requests>=2.28.0
aiohttp>=3.8.0

# 按键监听
keyboard>=0.13.5
pynput>=1.7.6

# 音频文件处理
soundfile>=0.12.1
librosa>=0.10.0
wave

# 配置和数据处理
json5>=0.9.0
pyyaml>=6.0

# 网络请求和API
urllib3>=1.26.0
httpx>=0.24.0

# 日志和调试
loguru>=0.7.0

# 可选：音频增强
noisereduce>=3.0.0
scipy>=1.9.0

# 可选：更好的音频设备管理
sounddevice>=0.4.6

# 开发和测试
pytest>=7.0.0
pytest-asyncio>=0.21.0
